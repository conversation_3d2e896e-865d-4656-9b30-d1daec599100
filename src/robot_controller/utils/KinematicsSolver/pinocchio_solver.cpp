#include "robot_controller/def_class.h"

namespace KINEMATICS {
    PinocchioSolver::PinocchioSolver(std::string robot_type, std::string side) {
        path_urdf_ = ros::package::getPath("robot_controller") + "/description/urdf/S1_robot.urdf";

        try {
            // 加载完整的机器人模型
            pinocchio::urdf::buildModel(path_urdf_, full_model_, false, true);

            // 构建单臂模型
            if (buildArmModel_(full_model_, side)) {
                // 检查末端执行器是否存在
                if (arm_model_.existFrame(end_effector_name_)) {
                    end_effector_id_ = arm_model_.getFrameId(end_effector_name_);

                    // 初始化关节限制（S1机器人右臂关节限制）
                    q_min_.resize(arm_model_.nq);
                    q_max_.resize(arm_model_.nq);

                    // 设置默认关节角度限制（单位：弧度）
                    for(int i = 0; i < arm_model_.nq; i++){
                        q_min_(i) = arm_model_.lowerPositionLimit[i];
                        q_max_(i) = arm_model_.upperPositionLimit[i];
                    }

                    initialized_ = true;
                    std::cout << BLUE << side << "Arm运动学求解器初始化成功" << "[Pinocchio " << PINOCCHIO_VERSION << "]\n" 
                              << "  |--关节数量: " << arm_model_.nq << "[active], " << arm_model_.njoints - arm_model_.nq - 1 << "[mimic]\n"
                              << "  |--末端执行器: " << end_effector_name_ << " (id: " << end_effector_id_ << ")\n"
                              << "  |--关节限制已设置" << RESET << std::endl;
                } else {
                    std::cerr << "错误：未找到末端执行器 " << end_effector_name_ << std::endl;
                }
            } else {
                std::cerr << "错误：构建机械臂模型失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "初始化运动学求解器时发生异常: " << e.what() << std::endl;
        }


    }
    PinocchioSolver::~PinocchioSolver(){}

    bool PinocchioSolver::buildArmModel_(const pinocchio::Model& full_model, std::string arm_side){
        // 通过排除法确定要锁定的关节：定义需要保留的关节，其余全部锁定
        std::vector<std::string> joints_to_keep;
        if(arm_side == "right"){
            // 只保留右臂的7个关节
            joints_to_keep.push_back("r_joint1");
            joints_to_keep.push_back("r_joint2");
            joints_to_keep.push_back("r_joint3");
            joints_to_keep.push_back("r_joint4");
            joints_to_keep.push_back("r_joint4_mimic");
            joints_to_keep.push_back("r_joint5");
            joints_to_keep.push_back("r_joint6");
            joints_to_keep.push_back("r_joint7");
            // 设置末端执行器名称
            end_effector_name_ = "r_Link7";
        }else if(arm_side == "left"){
            // 只保留左臂的7个关节
            joints_to_keep.push_back("l_joint1");
            joints_to_keep.push_back("l_joint2");
            joints_to_keep.push_back("l_joint3");
            joints_to_keep.push_back("l_joint4");
            joints_to_keep.push_back("l_joint4_mimic");
            joints_to_keep.push_back("l_joint5");
            joints_to_keep.push_back("l_joint6");
            joints_to_keep.push_back("l_joint7");
            // 设置末端执行器名称
            end_effector_name_ = "l_Link7";
        }
        else{
            std::cerr << "错误：未知的机械臂侧" << std::endl;
            return false;
        }
        // 通过排除法生成要锁定的关节列表
        std::vector<std::string> joints_to_lock_names;
        // 遍历模型中的所有关节，排除需要保留的关节
        for(int i = 1; i < full_model.njoints; i++) {  // 从1开始，跳过universe关节
            std::string joint_name = full_model.names[i];
            // 检查当前关节是否在保留列表中
            bool should_keep = false;
            for(const std::string& keep_joint : joints_to_keep) {
                if(joint_name == keep_joint) {
                    should_keep = true;
                    break;
                }
            }
            // 如果不在保留列表中，则添加到锁定列表
            if(!should_keep) {
                joints_to_lock_names.push_back(joint_name);
            }
        }

        try {
            // 获取需要锁定的关节列表
            std::vector<pinocchio::JointIndex> joints_to_lock_ids;
            for (const auto& joint_name : joints_to_lock_names) {
                if (full_model.existJointName(joint_name)) {
                    joints_to_lock_ids.push_back(full_model.getJointId(joint_name));
                }
            }
            // 设置锁定关节的位置为全0
            Eigen::VectorXd q_locked = Eigen::VectorXd::Zero(full_model.nq);
            // 构建单臂模型
            arm_model_ = pinocchio::buildReducedModel(full_model, joints_to_lock_ids, q_locked);
            arm_data_ = pinocchio::Data(arm_model_);
        } catch (const std::exception& e) {
            std::cerr << "构建单臂模型[" << arm_side << "]时发生异常: " << e.what() << std::endl;
            return false;
        }

        return true;
    }

    EndEffectorPose PinocchioSolver::fk_solver(const Eigen::VectorXd& q, bool print_res){
        EndEffectorPose pose;
        pose.position = Eigen::Vector3d::Zero();
        pose.euler = Eigen::Vector3d::Zero();
        pose.quaternion = Eigen::Quaterniond::Identity();
        pose.transform = Eigen::Matrix4d::Identity();

        if (!initialized_) {
            std::cerr << "错误：求解器未正确初始化" << std::endl;
            return pose;
        }

        if (q.size() != arm_model_.nq) {
            std::cerr << "错误：关节角度维度不匹配" << std::endl;
            return pose;
        }

        pinocchio::forwardKinematics(arm_model_, arm_data_, q);
        pinocchio::updateFramePlacements(arm_model_, arm_data_);

        pinocchio::SE3Tpl<double> se3 = arm_data_.oMf[end_effector_id_];

        // 平移
        pose.position = se3.translation();
        // 旋转
        Eigen::Matrix3d R = se3.rotation();
        // 欧拉角 (ZYX顺序: yaw, pitch, roll)
        pose.euler = R.eulerAngles(2, 1, 0);
        // 四元数
        pose.quaternion = Eigen::Quaterniond(R);
        // 齐次变换矩阵
        pose.transform.block<3,3>(0,0) = R;
        pose.transform.block<3,1>(0,3) = pose.position;

        if(print_res){
            ROS_INFO("position: [%.4f, %.4f, %.4f]", 
                     pose.position(0), pose.position(1), pose.position(2));
            ROS_INFO("euler(zyx): [%.4f, %.4f, %.4f](deg), [%.4f, %.4f, %.4f](rad)", 
                     pose.euler(0)/M_PI*180, pose.euler(1)/M_PI*180, pose.euler(2)/M_PI*180,
                     pose.euler(0), pose.euler(1), pose.euler(2)); 
            ROS_INFO("quaternion(xyzw): [%.4f, %.4f, %.4f, %.4f]", 
                     pose.quaternion.x(), pose.quaternion.y(), pose.quaternion.z(), pose.quaternion.w());
        }
        
        return pose;
    }


    bool PinocchioSolver::ik_solver(const EndEffectorPose& target_pose, const Eigen::VectorXd& q_init, Eigen::VectorXd& q_solution, bool accuracy_test, bool time_test, int max_iterations, double position_tolerance, double rotation_tolerance, double position_weight, double rotation_weight){
        if (!initialized_) {
            std::cerr << "错误：求解器未正确初始化" << std::endl;
            return false;
        }

        if (q_init.size() != arm_model_.nq) {
            std::cerr << "错误：初始关节角度维度不匹配，期望 " << arm_model_.nq
                    << "，实际 " << q_init.size() << std::endl;
            return false;
        }

        q_solution = q_init;

        pinocchio::SE3 target_se3(target_pose.quaternion.toRotationMatrix(), target_pose.position);
        // 自适应参数
        double adaptive_damping = damping_factor_;
        double adaptive_step_size = step_size_;
        double prev_error_norm = std::numeric_limits<double>::max();
        int stagnation_count = 0;

        for (int iter = 0; iter < max_iterations; ++iter) {
            // 计算当前关节角度下的正运动学
            pinocchio::forwardKinematics(arm_model_, arm_data_, q_solution);
            pinocchio::updateFramePlacements(arm_model_, arm_data_);

            // 获取当前末端位姿
            pinocchio::SE3 current_pose = arm_data_.oMf[end_effector_id_];

            // 计算位置误差
            Eigen::Vector3d pos_error = target_se3.translation() - current_pose.translation();

            // 计算旋转误差（使用更稳定的方法）
            Eigen::Matrix3d R_error = target_se3.rotation() * current_pose.rotation().transpose();
            Eigen::Vector3d rot_error = pinocchio::log3(R_error);

            // 组合误差向量，应用权重
            Eigen::VectorXd error(6);
            error.head<3>() = position_weight * pos_error;
            error.tail<3>() = rotation_weight * rot_error;

            double current_error_norm = error.norm();

            // 打印调试信息
            if (iter % 50 == 0) {
                std::cout << "迭代 " << iter << ": 位置误差 = " << pos_error.norm()
                        << ", 旋转误差 = " << rot_error.norm()
                        << ", 总误差 = " << current_error_norm << std::endl;
            }

            // 检查收敛条件
            if (pos_error.norm() < position_tolerance && rot_error.norm() < rotation_tolerance) {
                std::cout << "改进逆运动学求解成功，迭代次数: " << iter << std::endl;
                return true;  // 求解成功
            }

            // 自适应调整参数
            if (current_error_norm >= prev_error_norm) {
                stagnation_count++;
                if (stagnation_count > 5) {
                    // 增加阻尼，减小步长
                    adaptive_damping *= 2.0;
                    adaptive_step_size *= 0.8;
                    stagnation_count = 0;
                    std::cout << "调整参数: damping=" << adaptive_damping
                            << ", step_size=" << adaptive_step_size << std::endl;
                }
            } else {
                stagnation_count = 0;
                // 逐渐恢复参数
                adaptive_damping = std::max(adaptive_damping * 0.95, damping_factor_);
                adaptive_step_size = std::min(adaptive_step_size * 1.02, step_size_);
            }
            prev_error_norm = current_error_norm;

            // 计算雅可比矩阵
            Eigen::MatrixXd J(6, arm_model_.nv);
            pinocchio::computeFrameJacobian(arm_model_, arm_data_, q_solution,
                                        end_effector_id_, pinocchio::LOCAL_WORLD_ALIGNED, J);

            // 应用权重到雅可比矩阵
            J.topRows(3) *= position_weight;
            J.bottomRows(3) *= rotation_weight;

            // 使用自适应阻尼最小二乘法
            Eigen::MatrixXd JtJ = J.transpose() * J;
            Eigen::MatrixXd damped_JtJ = JtJ + adaptive_damping * Eigen::MatrixXd::Identity(JtJ.rows(), JtJ.cols());

            // 使用更稳定的求解方法
            Eigen::VectorXd dq;
            Eigen::LDLT<Eigen::MatrixXd> solver(damped_JtJ);
            if (solver.info() == Eigen::Success) {
                dq = solver.solve(J.transpose() * error);
            } else {
                // 如果LDLT失败，使用SVD
                Eigen::JacobiSVD<Eigen::MatrixXd> svd(J, Eigen::ComputeThinU | Eigen::ComputeThinV);
                dq = svd.solve(error);
            }

            // 限制步长
            double step_norm = dq.norm();
            if (step_norm > max_step_norm_) {
                dq = dq * (max_step_norm_ / step_norm);
            }

            // 更新关节角度
            q_solution = pinocchio::integrate(arm_model_, q_solution, adaptive_step_size * dq);

            // // 应用关节限制
            // if (use_joint_limits_) {
            //     clampJointAngles(q_solution);
            // }
        }

        std::cout << "改进逆运动学求解未收敛，已达最大迭代次数: " << max_iterations << std::endl;
        return false;  // 未收敛        
    }
}