#include "robot_controller/def_class.h"

int main(int argc, char *argv[]){
    setlocale(LC_ALL,"");

    ros::init(argc, argv, "test_pin_class");
    ros::NodeHandle nh;
    
    ROS_INFO_STREAM("测试 PinocchioSolver 类 初始化");
    KINEMATICS::PinocchioSolver RightArmSolver("S1_robot", "right");
    KINEMATICS::PinocchioSolver LeftArmSolver("S1_robot", "left");

    ROS_INFO_STREAM("测试 PinocchioSolver 类 正运动学");

    Eigen::VectorXd q_right(7);
    q_right << 0.308, 0.688, 0.295, 0.457, 0.161, 0.326, 0.598;
    RightArmSolver.fk_solver(q_right, true);

    return 0;
}