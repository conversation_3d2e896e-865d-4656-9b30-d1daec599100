#ifndef _DEF_CLASS_H
#define _DEF_CLASS_H

#include <pinocchio/config.hpp>
#include <pinocchio/parsers/urdf.hpp>
#include <pinocchio/multibody/model.hpp>
#include <pinocchio/multibody/data.hpp>
#include <pinocchio/multibody/joint/joint-mimic.hpp>
#include <pinocchio/algorithm/kinematics.hpp>
#include <pinocchio/algorithm/geometry.hpp>
#include <pinocchio/algorithm/jacobian.hpp>
#include <pinocchio/algorithm/frames.hpp>
#include <pinocchio/algorithm/joint-configuration.hpp>

#include "def_struct.h"

#include <stdio.h>
#include <iostream>
#include <fstream>
#include <string>

#include <serial/serial.h>

#include <chrono>
#include <ctime>

#include <mutex>
#include <future>
#include <thread>

#include <kdl/kdl.hpp> 
#include <kdl/chain.hpp> 
#include <kdl/tree.hpp> 
#include <kdl_parser/kdl_parser.hpp> 
#include <kdl/chainfksolverpos_recursive.hpp> 
#include <kdl/chainiksolverpos_lma.hpp>
#include <kdl/chainiksolver.hpp>
#include <kdl/chainfksolver.hpp>

#include <trac_ik/trac_ik.hpp>
#include <urdf/model.h>
#include <yaml-cpp/yaml.h>

#include "relaxed_ik_wrapper/relaxed_ik_wrapper.h"

#include <tf2/LinearMath/Quaternion.h>
#include <tf2/LinearMath/Matrix3x3.h>

#include <std_msgs/Int8.h>
#include <std_msgs/Bool.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Float32MultiArray.h>
#include <std_msgs/Float64.h>
#include <std_msgs/Float32.h>
#include <sensor_msgs/JointState.h>
#include <geometry_msgs/PoseArray.h>
#include <geometry_msgs/PoseStamped.h>


#include "ros/ros.h"
#include <ros/package.h>
#include "ros/callback_queue.h"

#define RESET   "\033[0m"
#define BOLD    "\033[1m"
#define RED     "\033[31m"
#define GREEN   "\033[32m"
#define YELLOW  "\033[33m"
#define BLUE    "\033[34m"
#define MAGENTA "\033[35m"
#define CYAN    "\033[36m"
#define WHITE   "\033[37m"

using Vector7d = Eigen::Matrix<double, 7, 1>;

namespace FILTER {
    class SecLowPassFilter {
        public:
            SecLowPassFilter();
            void setParam(double cutoffFrequency, double sampleRate, int dim = 7, double Q = 0.7071);
            Eigen::VectorXd process(Eigen::VectorXd input);
            void reset();
        private:
            int state_dim_ = 0;
            // 滤波器系数
            Eigen::VectorXd b0_, b1_, b2_, a1_, a2_;
            // 过去的输入/输出状态变量
            Eigen::VectorXd x1_, x2_;
            Eigen::VectorXd y1_, y2_;
    };
}

namespace KINEMATICS {
    class SpatialTransform {
        public:
            Eigen::Matrix3d rot_x(double angle, bool degrees = false);
            Eigen::Matrix3d rot_y(double angle, bool degrees = false);
            Eigen::Matrix3d rot_z(double angle, bool degrees = false);

            Eigen::Vector3d quaternionToEulerAngles(Eigen::Vector4d quat, bool degrees = true, std::string sequence = "zyx");
            Eigen::Vector4d eulerAnglesToQuaternion(Eigen::Vector3d euler, bool degrees = true, std::string sequence = "zyx");
            Eigen::Matrix3d eulerAnglesToRotationMatrix(Eigen::Vector3d euler, bool degrees = true, std::string sequence = "zyx");
            Eigen::Vector3d rotationMatrixToEulerAngles(Eigen::Matrix3d rotMatrix, bool degrees = true, std::string sequence = "zyx");
            Eigen::Matrix3d quaternionToRotationMatrix(Eigen::Vector4d quat);
            Eigen::Vector4d rotationMatrixToQuaternion(Eigen::Matrix3d rotMatrix);

            Eigen::Matrix4d transformationMatrix(double d, double a, double alpha, double offset, double theta);
            
            Eigen::Matrix4d getTransformationMatrix(const Eigen::Vector3d& translation, const Eigen::Vector3d& eulerAngles, std::string sequence = "xyz");

            Eigen::Matrix<double,6,1> poseTransform(Eigen::Matrix<double,6,1> ori_pose, Eigen::Matrix<double,6,1> rel_pose);

    };
    class Unit_Conv {
        public:
            double deg2rad(double a);
            Eigen::VectorXd deg2rad(Eigen::VectorXd vec);
            std::vector<double> deg2rad(std::vector<double> vec);
            double rad2deg(double a);
            Eigen::VectorXd rad2deg(Eigen::VectorXd vec);
            std::vector<double> rad2deg(std::vector<double> vec);

            void handAngle2Pos(std::vector<double>& vec);
            void handAngle2Pos(Eigen::VectorXd& vec);
            std::vector<int> handPos2Angle(std::vector<double> vec);
            Eigen::VectorXi handPos2Angle(Eigen::VectorXd vec);
    };

    /**
    * @brief Pinocchio运动学求解器类
    * 
    * 该类提供了基于Pinocchio库的正逆运动学求解功能，
    * 逆运动学使用数值迭代方法求解给定末端位姿对应的关节角度。
    * 
    */
    class PinocchioSolver {
        public:
            /**
             * @brief 构造函数
             * @param robot_type 机器人类型，目前只有"S1"
             * @param side 机械臂侧，"right"或"left"
             */
            PinocchioSolver(std::string robot_type, std::string side);
            /**
             * @brief 析构函数
             */
            ~PinocchioSolver();

            /**
             * @brief 正运动学求解器
             * @param q 关节角度（7维向量）
             * @param print_res 是否打印结果
             * @return 末端执行器位姿
             */
            EndEffectorPose fk_solver(const Eigen::VectorXd& q, bool print_res = false);

            /**
             * @brief 逆运动学求解器（位置和姿态都高精度，带关节限制）
             * @param target_pose 目标末端位姿（SE3变换）
             * @param q_init 初始关节角度（7维向量）
             * @param q_solution 输出的关节角度解（7维向量）
             * @param accuracy_test 是否进行准确度测试（可选），布尔值。计算求解出的那组关节角度经过正运动学计算后，末端位姿与目标位姿的差距。
             * @param time_test 是否进行耗时测试（可选），布尔值。计算逆解耗时。
             * @param max_iterations 最大迭代次数，默认1000
             * @param position_tolerance 位置收敛精度，默认1e-4
             * @param rotation_tolerance 旋转收敛精度，默认1e-4
             * @param position_weight 位置误差权重，默认1.0
             * @param rotation_weight 旋转误差权重，默认0.5
             * @return 是否求解成功
             */
            bool ik_solver(const EndEffectorPose& target_pose,
                           const Eigen::VectorXd& q_init,
                           Eigen::VectorXd& q_solution,
                           bool accuracy_test = false,
                           bool time_test = false,
                           int max_iterations = 1000,
                           double position_tolerance = 1e-4,
                           double rotation_tolerance = 1e-4,
                           double position_weight = 1.0,
                           double rotation_weight = 0.5);

        private:
            std::string path_urdf_;                     // URDF文件路径
            pinocchio::Model full_model_;               // 完整机器人模型
            pinocchio::Model arm_model_;                // 机械臂模型
            pinocchio::Data arm_data_;                  // 臂数据
            pinocchio::FrameIndex end_effector_id_;     // 末端执行器frame ID
            std::string end_effector_name_;             // 末端执行器名称
            bool initialized_;                          // 初始化状态

            // 求解器参数
            double damping_factor_;                     // 阻尼系数
            double step_size_;                          // 步长
            double max_step_norm_;                      // 最大步长范数

            // 关节限制
            Eigen::VectorXd q_min_;                     // 关节角度下限
            Eigen::VectorXd q_max_;                     // 关节角度上限
            bool use_joint_limits_;                     // 是否使用关节限制

            /**
             * @brief 构建右/左臂模型（锁定其他关节）
             * @param full_model 完整机器人模型
             * @param arm_side 机械臂侧，"right"或"left"
             * @return 是否构建成功
             */
            bool buildArmModel_(const pinocchio::Model& full_model, std::string arm_side);
    };

    class Relaxed_IK_Solver {
        public:
            Relaxed_IK_Solver(std::string robot_type, std::string side);
            ~Relaxed_IK_Solver();
            JntPos ik_solver(Eigen::Vector3d cart_pos, Eigen::Vector3d eular, bool accuracy_test = false, bool time_test = false);
            JntPos ik_solver(Eigen::Matrix<double, 6, 1> ee_vel);
            KDL::Frame fk_solver(Eigen::VectorXd jnt_pos, bool print_res = false);

        public:
            YAML::Node config;

            std::string setting_file_path;
            std::string path_urdf;
            std::string base_link;
            std::string tip_link;

            unsigned int nj;

            std::vector<double> tolerances;
            RelaxedIKRust* relaxed_ik_solver;

            KDL::Tree my_tree;
            KDL::Chain chain;
            KDL::ChainFkSolverPos_recursive fksolver = KDL::ChainFkSolverPos_recursive(chain);

    };

    class TRAC_IK_Solver {
        public:
            TRAC_IK_Solver(std::string robot_type, std::string side, TRAC_IK::SolveType solve_type = TRAC_IK::Distance);
            ~TRAC_IK_Solver();
            JntPos ik_solver(Eigen::Vector3d cart_pos, Eigen::Vector3d eular, Vector7d qpos_init = Eigen::MatrixXd::Zero(7,1), bool accuracy_test = false, bool time_test = false);
            KDL::Frame fk_solver(Eigen::VectorXd jnt_pos, bool print_res = false);

        public:
            std::string arm_type; 
            std::string arm_side;

            std::string path_yaml;
            YAML::Node config;

            std::string path_urdf;

            std::string base_link;
            std::string tip_link;

            double z_bias;
            
            unsigned int nj;
            double eps = 1e-10;
            double timeout = 0.005;

            KDL::Tree my_tree;
            KDL::Chain chain;

            TRAC_IK::TRAC_IK* iksolver;
            KDL::ChainFkSolverPos_recursive fksolver = KDL::ChainFkSolverPos_recursive(chain);
    };
}
// namespace DATA_PROC {
//     extern Eigen::VectorXd jnt_pos_actual_r;
//     extern Eigen::VectorXd jnt_pos_actual_l;

//     extern Eigen::VectorXd jnt_pos_target_r;
//     extern Eigen::VectorXd jnt_pos_target_l;

//     extern Eigen::VectorXd motors_current_r;
//     extern Eigen::VectorXd motors_current_l;

//     extern Eigen::VectorXd ee_vel_teleop;

//     void fileWrite(FILE *fpWrite, Eigen::VectorXd vec_r, Eigen::VectorXd vec_l);
//     void printJointAngles(const std::string &arm_side, const Eigen::VectorXd &jnt_pos);

//     class Arm_Control {
//         public:
//             Arm_Control(bool flag_colli_det = true, std::string robot_type = "gen2");
//             ~Arm_Control();

//             void move(ArmTraj traj, int isSim, double control_frequency = 1000, std::string robot_type = "gen2");

//         private:
//             ros::NodeHandle nh_;
//             // 话题发布对象
//             ros::Publisher pub_motor_;
//             ros::Publisher pub_rviz_;
//             // 话题数据载体
//             sensor_msgs::JointState rviz_msgs_;
//             std_msgs::Float64MultiArray motor_msgs_;
            
//             // 文件指针
//             FILE *fwPubPos_;
//             FILE *fwPubVel_;
//             FILE *fwPubAcc_;
//     };

//     class Data_Sub {
//         public:
//             Data_Sub();
//             ~Data_Sub();
//         public:
//             void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg);
//             void camera_arrayCallback_base(const std_msgs::Float64MultiArray::ConstPtr& msg);
//             void clothes_points_arrayCallback(const std_msgs::Float32MultiArray::ConstPtr& msg);
//             void ee_vel_goals_callback(const std_msgs::Float32MultiArray::ConstPtr& msg);

//             void getJntPos(Vector7d &jnt_pos_right, Vector7d &jnt_pos_left, bool print_res = false);
//             void getCartPos_Right(Eigen::Vector3d &position, Eigen::Vector3d &eular, KINEMATICS::KDL_Solver *kine_solver, bool print_res = false);
//             void getCartPos_Left(Eigen::Vector3d &position, Eigen::Vector3d &eular, KINEMATICS::KDL_Solver *kine_solver, bool print_res = false);

//             void waitForCVData();
//             bool waitForCVData1(size_t expected_size);
//             bool waitForCVData2(size_t expected_size);
//             void extractTargetPosition(Eigen::Vector3d &target_pos, size_t start_index);
//             void getCVPos(Eigen::Vector3d &target_pos);
//             void getCVPos(Eigen::Vector3d &target_pos, int &obj_type);
//             void getCVPos(Eigen::Vector3d &target_pos1, Eigen::Vector3d &target_pos2);
//             void getCVPos(Eigen::Vector3d &target_pos1, int &obj_type1, Eigen::Vector3d &target_pos2, int &obj_type2);

//             void getClothesPos(Eigen::Vector3d &target_pos1, Eigen::Vector3d &target_pos2, int service_call_times = 1);

//             void getMedicinePose(std::string type_medicine, Eigen::Vector3d &position, double &angle, int &res_match);

//             void getKeyboardCmd(Eigen::VectorXd &qpos, KINEMATICS::Relaxed_IK_Solver *rlx_solver);
//         public:    
//             std::vector<double> container1;
//             std::vector<double> container2;
//             bool ActPos_flag;
//             bool subCV_flag1;
//             bool subCV_flag2;
//         private:
//             ros::NodeHandle nh1_;
//             ros::NodeHandle nh2_;
//             ros::NodeHandle nh3_;
//             ros::NodeHandle nh4_;

//             ros::ServiceClient callService_;

//             ros::Subscriber subActPos_;
//             ros::Subscriber subCVPos1_;
//             ros::Subscriber subCVPos2_;
//             ros::Subscriber subEEVelGoals_;

//             ros::CallbackQueue* queue1_;
//             ros::CallbackQueue* queue2_;
//             ros::CallbackQueue* queue4_;

//             ros::AsyncSpinner* spinner1_;
//             ros::AsyncSpinner* spinner2_;
//             ros::AsyncSpinner* spinner4_;

//             FILE *fwSubPos_;
//     };
//     class Data_Sub_TeleOp {
//         public:
//             Data_Sub_TeleOp(bool flag_ee_det = false, std::string arm = "gen2");
//             ~Data_Sub_TeleOp();
//         public:
//             void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg);
//             void getJntPos(Eigen::VectorXd &jnt_pos_right, Eigen::VectorXd &jnt_pos_left);

//             void cvPoseCallback(const geometry_msgs::PoseArray::ConstPtr& msg);
//             void eePoseCallbackLeft(const geometry_msgs::PoseStamped::ConstPtr& msg);
//             void eePoseCallbackRight(const geometry_msgs::PoseStamped::ConstPtr& msg);
//             void getTargetPos(Eigen::VectorXd &jnt_pos_right, Eigen::VectorXd &jnt_pos_left, bool &flag_end);

//         public:    
//             std::string arm_type;
//             bool ActPos_flag;
//             bool subCV_flag;
//             int num_subCV;
//             ros::Subscriber subActPos_;
//             ros::Subscriber subCVPos_;
//             ros::Subscriber subEEPoseLeft_;
//             ros::Subscriber subEEPoseRight_;

//             RelaxedIKRust* left_arm_ik;
//             RelaxedIKRust* right_arm_ik;
//             std::string path_package;
//             std::string setting_file_path1;
//             std::string setting_file_path2;

//             FILTER::SecLowPassFilter sec_lpFilter_l;
//             FILTER::SecLowPassFilter sec_lpFilter_r; 
//         private:
//             ros::NodeHandle nh1_;
//             ros::NodeHandle nh2_;
//             ros::NodeHandle nh3_;

//             ros::CallbackQueue* queue1_;
//             ros::CallbackQueue* queue2_;
//             ros::CallbackQueue* queue3_;

//             ros::AsyncSpinner* spinner1_;
//             ros::AsyncSpinner* spinner2_;
//             ros::AsyncSpinner* spinner3_;
            
//             FILE *fwSubPos_;
//     };
// }

// namespace ANOM_DETEC {
//     class AnomalyDetection {
//         public:    
//             bool isExceedPosLimit(Eigen::VectorXd& pos_r, Eigen::VectorXd& pos_l);
//             bool isExceedVelLimit(const Eigen::VectorXd& vel_r, const Eigen::VectorXd& vel_l, double limit = 1000.0);
//     };
// }



// namespace TRAJ_PLAN {
//     class Interpolation{
//         public:

//             Plan_Res quinitic_poly(Eigen::VectorXd t_seg, Eigen::VectorXd pos_seg, double dt);
//             Plan_Res quinitic_poly(double T, double p0, double pn, double dt, double v0, double vn, double a0, double an);

//             Plan_Res cub_spline(Eigen::VectorXd t_seg, Eigen::VectorXd pos_seg, double dt, double v0, double vn);

            
            
//             Plan_Res_MulDOF JointSpaceP2P(Vector7d& jnt_pos_init, JointInterPoints ips, int inter_method = 0);
//             Plan_Res_MulDOF JointSpaceP2P(Vector7d& jnt_pos_init, CartInterPoints ips, KINEMATICS::TRAC_IK_Solver kine_solver, int inter_method = 0);
//             Plan_Res_MulDOF JointSpaceP2P(Vector7d& jnt_pos_init, CartInterPoints ips, KINEMATICS::KDL_Solver *kine_solver, int inter_method = 0, Eigen::Vector3d ee_bias = Eigen::Vector3d::Zero());
            
//             Plan_Res_MulDOF JointSpaceP2P_Continuous(JntEnd& init, JointInterPoints_Continuous ips, double n_mult);

//             Plan_Res_MulDOF CartSpaceP2P_Line(Vector7d& jnt_pos_init, CartInterPoints ips, KINEMATICS::KDL_Solver *kine_solver, Eigen::Vector3d ee_bias = Eigen::Vector3d::Zero());
            
//             Plan_Res_MulDOF stay_still(Vector7d& jnt_pos, int n, double dt);
//     };         
    
//     class DMP{
//         public:
//             Plan_Res_MulDOF action1_dmp(Eigen::VectorXd& jnt_end, DmpTargetPoints target, bool check_start_pos = true, bool ikine_info_show = false, bool plan_info_show = false);
//         private:
//             Demon_Traj dataExt_(std::string pathName1, std::string pathName2);
//             Train_Res dmpTrain_(Demon_Traj data, Train_Par par);
//             Plan_Res_MulDOF lnrCmp_(Plan_Res_MulDOF traj, Eigen::Vector4d g);
//             Plan_Res_MulDOF dmpRegress_(Train_Res trainRes, Regress_Par par);
//             Plan_Res_MulDOF limitAmplitude_(Plan_Res_MulDOF traj, Eigen::Vector4d y0, Eigen::Vector4d g, Eigen::Vector4d jntLimit);
//             Plan_Res_MulDOF dmp_(std::string pathJntPos, std::string pathAngArm, Train_Par trainPar, Eigen::VectorXd theta_init, DmpTargetPoints target, Eigen::Vector4d jntLimit, bool ikine_info_show = false, bool plan_info_show = false);
//     };

//     class Traj_Adjust{
//         public:
//             Plan_Res_MulDOF traj_splic(Vector7d& jnt_end, Plan_Res_MulDOF action1, Plan_Res_MulDOF action2);
//             Plan_Res_MulDOF traj_extend(Plan_Res_MulDOF action, int n, double dt);
//             Plan_Res_MulDOF traj_inv(Vector7d& jnt_end, Plan_Res_MulDOF traj_ori);
//     };
// }

// namespace Agilex{
//     class ranger_mini_3{
//         public:
//             ranger_mini_3();
//             ~ranger_mini_3();

//             void vehicle_move(double distance_x, double distance_y, double duration);

//         private:
//             ros::NodeHandle nh_;
//             ros::Publisher pub_trans_cmd_;
//             std_msgs::Float32MultiArray msg_trans_cmd_;
//     };
// }



#endif