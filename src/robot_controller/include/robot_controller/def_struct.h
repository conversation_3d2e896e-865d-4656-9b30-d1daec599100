#ifndef _DEF_STRUCT_H
#define _DEF_STRUCT_H

#include <vector>

#include <Eigen/Core>
#include <Eigen/Dense>
#include <Eigen/LU>
#include <stdexcept> // For std::out_of_range

#include <visualization_msgs/Marker.h>
#include <geometry_msgs/Pose.h>

#include <memory>

struct ArmPose{
    Eigen::Vector3d pos_shoulder; 
    Eigen::Vector3d pos_elbow; 
    Eigen::Vector3d pos_wrist;
    Eigen::Vector3d pos_end;
    Eigen::Vector3d rpy; 
    std::string side;
};

struct CartInterPoints{
    std::vector<double> time_p;
    std::vector<double> armJ_p;
    std::vector<std::vector<double>> pos_p;
    std::vector<std::vector<double>> pose_p;
    double dt;
};
struct JointInterPoints{
    std::vector<double> time_p;
    std::vector<std::vector<double>> qpos_p;
    double dt;
};
struct JointInterPoints_Continuous{
    std::vector<double> time_p;
    std::vector<std::vector<double>> qpos_p;
    std::vector<std::vector<double>> qvel_p;
    std::vector<std::vector<double>> qacc_p;
    double dt;
};
struct DmpTargetPoints{
    std::vector<double> pos_p;
    std::vector<double> pose_p;
    double dt;
    double armJ_p = -3.141592653589793238462643383279/2;
    double tau = 1;
};

struct JntEnd{
    Eigen::VectorXd pos;
    Eigen::VectorXd vel;
    Eigen::VectorXd acc;
};


struct Plan_Res_MulDOF
{
    Eigen::MatrixXd pos;
    Eigen::MatrixXd vel;
    Eigen::MatrixXd acc;
    Eigen::VectorXd t;
    int n;
};
struct Plan_Res
{
    Eigen::VectorXd pos;
    Eigen::VectorXd vel;
    Eigen::VectorXd acc;
    Eigen::VectorXd t;
};

/**
 * @struct JntPos
 * @brief 用于存储关节位置信息的结构体
 */
struct JntPos {
    Eigen::VectorXd theta;      // 存储关节角度的向量[rad]
    int error_flag = 0;         // 错误标志位
    int solved_flag = 0;        // 解决标志位
};

/**
 * @struct EndEffectorPose
 * @brief 用于存储末端执行器位姿信息的结构体
 */
struct EndEffectorPose {
    Eigen::Vector3d position;      // 位置 (x, y, z)
    Eigen::Vector3d euler;         // 欧拉角 (roll, pitch, yaw)
    Eigen::Quaterniond quaternion; // 四元数
    Eigen::Matrix4d transform;     // 齐次变换矩阵
};

struct ArmTraj{
    Plan_Res_MulDOF right;
    Plan_Res_MulDOF left;
};


template<typename T>
class DynamicArray {
private:
    T* data;         // 指向动态分配的数组
    size_t capacity; // 数组的容量
    size_t length;   // 数组当前长度

public:
    // 构造函数
    DynamicArray(size_t initial_capacity = 10) : capacity(initial_capacity), length(0) {
        data = new T[capacity];
    }
    // 析构函数
    ~DynamicArray() {
        delete[] data;
    }
    // 拷贝构造函数
    DynamicArray(const DynamicArray& other) : capacity(other.capacity), length(other.length) {
        data = new T[capacity];
        for (size_t i = 0; i < length; ++i) {
            data[i] = other.data[i];
        }
    }
    // 赋值运算符
    DynamicArray& operator=(const DynamicArray& other) {
        if (this != &other) {
            delete[] data;
            capacity = other.capacity;
            length = other.length;
            data = new T[capacity];
            for (size_t i = 0; i < length; ++i) {
                data[i] = other.data[i];
            }
        }
        return *this;
    }
    // 添加元素
    void push_back(const T& value) {
        if (length == capacity) {
            reserve(capacity * 2);
        }
        data[length++] = value;
    }
    // 获取元素
    T& operator[](size_t index) {
        if (index >= length) {
            throw std::out_of_range("Index out of range");
        }
        return data[index];
    }
    // 获取常量元素
    const T& operator[](size_t index) const {
        if (index >= length) {
            throw std::out_of_range("Index out of range");
        }
        return data[index];
    }
    // 获取数组长度
    size_t size() const {
        return length;
    }
    // 调整数组容量
    void reserve(size_t new_capacity) {
        if (new_capacity > capacity) {
            T* new_data = new T[new_capacity];
            for (size_t i = 0; i < length; ++i) {
                new_data[i] = data[i];
            }
            delete[] data;
            data = new_data;
            capacity = new_capacity;
        }
    }
};

#endif