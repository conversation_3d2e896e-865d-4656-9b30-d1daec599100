Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/S1_robot/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_e2fe6/fast && /usr/bin/make  -f CMakeFiles/cmTC_e2fe6.dir/build.make CMakeFiles/cmTC_e2fe6.dir/build
make[1]: 进入目录“/home/<USER>/S1_robot/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_e2fe6.dir/src.c.o
/usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_e2fe6.dir/src.c.o -c /home/<USER>/S1_robot/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_e2fe6
/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_e2fe6.dir/link.txt --verbose=1
/usr/bin/cc -rdynamic CMakeFiles/cmTC_e2fe6.dir/src.c.o -o cmTC_e2fe6 
/usr/bin/ld: CMakeFiles/cmTC_e2fe6.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x5e): undefined reference to `pthread_cancel'
/usr/bin/ld: src.c:(.text+0x6f): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_e2fe6.dir/build.make:99：cmTC_e2fe6] 错误 1
make[1]: 离开目录“/home/<USER>/S1_robot/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:127：cmTC_e2fe6/fast] 错误 2


Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/S1_robot/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_b9f3a/fast && /usr/bin/make  -f CMakeFiles/cmTC_b9f3a.dir/build.make CMakeFiles/cmTC_b9f3a.dir/build
make[1]: 进入目录“/home/<USER>/S1_robot/build/CMakeFiles/CMakeTmp”
Building C object CMakeFiles/cmTC_b9f3a.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create -o CMakeFiles/cmTC_b9f3a.dir/CheckFunctionExists.c.o -c /usr/local/share/cmake-3.23/Modules/CheckFunctionExists.c
Linking C executable cmTC_b9f3a
/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_b9f3a.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create -rdynamic CMakeFiles/cmTC_b9f3a.dir/CheckFunctionExists.c.o -o cmTC_b9f3a  -lpthreads 
/usr/bin/ld: 找不到 -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_b9f3a.dir/build.make:99：cmTC_b9f3a] 错误 1
make[1]: 离开目录“/home/<USER>/S1_robot/build/CMakeFiles/CMakeTmp”
make: *** [Makefile:127：cmTC_b9f3a/fast] 错误 2



