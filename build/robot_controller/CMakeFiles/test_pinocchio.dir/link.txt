/usr/bin/c++ -rdynamic CMakeFiles/test_pinocchio.dir/src/module_test/test_pinocchio.cpp.o -o /home/<USER>/S1_robot/devel/lib/robot_controller/test_pinocchio   -L/opt/ros/noetic/lib  -Wl,-rpath,/opt/ros/noetic/lib:/home/<USER>/S1_robot/devel/lib:/usr/lib/gcc/x86_64-linux-gnu/11:/home/<USER>/miniconda3/envs/pin/lib:/opt/ros/noetic/lib/x86_64-linux-gnu /home/<USER>/S1_robot/devel/lib/librobot_ctrl_lib.so /opt/ros/noetic/lib/libeigen_conversions.so /opt/ros/noetic/lib/libkdl_parser.so -lorocos-kdl /opt/ros/noetic/lib/liburdf.so -lurdfdom_sensor -lurdfdom_model_state -lurdfdom_model -lurdfdom_world -ltinyxml /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 /opt/ros/noetic/lib/librosconsole_bridge.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 /opt/ros/noetic/lib/libserial.so /opt/ros/noetic/lib/libkdl_parser.so -lorocos-kdl /opt/ros/noetic/lib/liburdf.so -lurdfdom_sensor -lurdfdom_model_state -lurdfdom_model -lurdfdom_world -ltinyxml /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 /opt/ros/noetic/lib/librosconsole_bridge.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 -lorocos-kdl /opt/ros/noetic/lib/libtrac_ik.so -lnlopt /opt/ros/noetic/lib/libkdl_parser.so -lorocos-kdl /opt/ros/noetic/lib/liburdf.so -lurdfdom_sensor -lurdfdom_model_state -lurdfdom_model -lurdfdom_world -ltinyxml /opt/ros/noetic/lib/libclass_loader.so -lPocoFoundation -ldl /opt/ros/noetic/lib/libroslib.so /opt/ros/noetic/lib/librospack.so -lpython3.8 /usr/lib/x86_64-linux-gnu/libboost_program_options.so.1.71.0 -ltinyxml2 /opt/ros/noetic/lib/librosconsole_bridge.so /opt/ros/noetic/lib/libroscpp.so -lpthread /usr/lib/x86_64-linux-gnu/libboost_chrono.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_filesystem.so.1.71.0 /opt/ros/noetic/lib/librosconsole.so /opt/ros/noetic/lib/librosconsole_log4cxx.so /opt/ros/noetic/lib/librosconsole_backend_interface.so -llog4cxx /usr/lib/x86_64-linux-gnu/libboost_regex.so.1.71.0 /opt/ros/noetic/lib/libroscpp_serialization.so /opt/ros/noetic/lib/libxmlrpcpp.so /opt/ros/noetic/lib/librostime.so /usr/lib/x86_64-linux-gnu/libboost_date_time.so.1.71.0 /opt/ros/noetic/lib/libcpp_common.so /usr/lib/x86_64-linux-gnu/libboost_system.so.1.71.0 /usr/lib/x86_64-linux-gnu/libboost_thread.so.1.71.0 /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 /usr/lib/x86_64-linux-gnu/libyaml-cpp.so.0.6.2 -lurdfdom_sensor -lurdfdom_model_state -lurdfdom_model -lurdfdom_world /opt/ros/noetic/lib/libserial.so -lorocos-kdl /opt/ros/noetic/lib/libtrac_ik.so -lnlopt /home/<USER>/miniconda3/envs/pin/lib/libpinocchio_parsers.so.3.6.0 -lurdfdom_sensor -lurdfdom_model_state -lurdfdom_model -lurdfdom_world /usr/lib/x86_64-linux-gnu/libconsole_bridge.so.0.4 /home/<USER>/miniconda3/envs/pin/lib/libsdformat14.so.14.8.0 /home/<USER>/miniconda3/envs/pin/lib/libgz-math7.so.7.5.2 /home/<USER>/miniconda3/envs/pin/lib/libgz-utils2.so.2.2.0 /home/<USER>/miniconda3/envs/pin/lib/libpinocchio_collision.so.3.6.0 /usr/lib/gcc/x86_64-linux-gnu/11/libgomp.so -lpthread /home/<USER>/miniconda3/envs/pin/lib/libpinocchio_extra.so.3.6.0 /home/<USER>/miniconda3/envs/pin/lib/libqhullcpp.a /home/<USER>/miniconda3/envs/pin/lib/libqhull_r.so.8.0.2 -lm /home/<USER>/miniconda3/envs/pin/lib/libpinocchio_visualizers.so.3.6.0 /home/<USER>/miniconda3/envs/pin/lib/libpinocchio_default.so.3.6.0 /opt/ros/noetic/lib/x86_64-linux-gnu/libhpp-fcl.so /home/<USER>/miniconda3/envs/pin/lib/libboost_filesystem.so /home/<USER>/miniconda3/envs/pin/lib/libboost_serialization.so /home/<USER>/miniconda3/envs/pin/lib/libboost_chrono.so /opt/ros/noetic/lib/liboctomap.so.1.9.8 /opt/ros/noetic/lib/liboctomath.so.1.9.8 
